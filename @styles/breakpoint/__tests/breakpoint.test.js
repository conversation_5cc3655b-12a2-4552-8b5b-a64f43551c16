/* eslint-disable no-useless-escape */

import breakpoint from '../breakpoint';
import laptop from '../laptop';
import tabletPortrait from '../tablet-portrait';
import desktop from '../desktop';

const css = 'width: 200px';

describe('styles/breakpoint', () => {
  it('supports unknown ranges', () => {
    const result = breakpoint('dummy', css);
    expect(result).not.toContain('@media');
    expect(result).toContain(css);
  });
  it('supports phone', () => {
    const type = 'max-width';
    const value = 599;
    const expectedResult = `@media (${type}: ${value}px) {`;
    const result = breakpoint('phone', css);
    expect(result).toContain(expectedResult);
    expect(result).toContain(css);
  });
  it('supports tablet', () => {
    const type = 'min-width';
    const value = 600;
    const expectedResult = `@media (${type}: ${value}px) {`;
    const result = breakpoint('tablet', css);
    expect(result).toContain(expectedResult);
    expect(result).toContain(css);
  });
  it('supports laptop', () => {
    const type = 'min-width';
    const value = 1200;
    const expectedResult = `@media (${type}: ${value}px) {`;
    const result = laptop(css);
    expect(result).toContain(expectedResult);
    expect(result).toContain(css);
  });
  it('supports desktop', () => {
    const type = 'min-width';
    const value = 1800;
    const expectedResult = `@media (${type}: ${value}px) {`;
    const result = desktop(css);
    expect(result).toContain(expectedResult);
    expect(result).toContain(css);
  });
  it('supports tabletPortrait', () => {
    const type = 'min-width';
    const value = 800;
    const expectedResult = `@media (${type}: ${value}px) {`;
    const result = tabletPortrait(css);
    expect(result).toContain(expectedResult);
    expect(result).toContain(css);
  });
});
