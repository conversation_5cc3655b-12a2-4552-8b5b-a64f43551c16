import { renderHook } from '@testing-library/react';
import { waitFor } from '@testing-library/react';
import { useOnScreen } from '@hooks/use-on-screen';
import { useOnScreenWithDelayOnce } from '../use-on-screen-with-delay-once';

const mockUseOnScreen: jest.Mock = useOnScreen as jest.Mock;

jest.mock('@hooks/use-on-screen', () => {
  const mockRef = {};
  return {
    useOnScreen: jest.fn().mockImplementation(({ onIntersectionChange }) => {
      onIntersectionChange(false);
      return mockRef;
    }),
  };
});

describe('@hooks/use-on-screen-with-delay-once', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('should invoke callback only once when item scrolls into view', () => {
    const pauseTime = 20;
    const waitForVisible = 10;

    const options = {
      callback: jest.fn(),
      delay: waitForVisible, // 10ms view for
    };

    const { rerender } = renderHook(() =>
      useOnScreenWithDelayOnce<HTMLDivElement>(options as any),
    );
    jest.advanceTimersByTime(pauseTime);

    // Bring into view and wait for timeout
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    // 'Hide' and wait
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(false);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    // Show and wait again
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    expect(options.callback).toHaveBeenCalledTimes(1);
  });

  it('should not invoke callback when in view for less than display time', async () => {
    // Waiting for more time than paused
    const pauseTime = 10;
    const waitForVisible = 20;

    const options = {
      callback: jest.fn(),
      delay: waitForVisible,
    };
    const { rerender } = renderHook(() =>
      useOnScreenWithDelayOnce<HTMLDivElement>(options as any),
    );

    // Bring into view and wait 100ms (less than waitForVisible)
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    // 'Hide' and wait
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(false);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    // Show and wait again (for less time than is required to trigger a call)
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    await waitFor(() => {
      expect(options.callback).toHaveBeenCalledTimes(0);
    });
  });

  it('should invoke callback with default delay', async () => {
    const pauseTime = 1100; // Default is 1000ms

    const options = {
      callback: jest.fn(),
    };

    const { rerender } = renderHook(() =>
      useOnScreenWithDelayOnce<HTMLDivElement>(options as any),
    );

    // Bring into view and wait for longer than pause
    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return {};
    });
    rerender();
    jest.advanceTimersByTime(pauseTime);

    await waitFor(() => {
      expect(options.callback).toHaveBeenCalledTimes(1);
    });
  });

  it('should return ref from `useOnScreen`', () => {
    const options = {
      callback: jest.fn(),
    };

    const { rerender, result } = renderHook(() =>
      useOnScreenWithDelayOnce<HTMLDivElement>(options as any),
    );

    const mockRef = {
      foo: 'bar',
    };

    mockUseOnScreen.mockImplementationOnce(({ onIntersectionChange }) => {
      onIntersectionChange(true);
      return mockRef;
    });

    rerender();

    expect(result.current).toEqual(mockRef);
  });

  it('should pass through options to the IntersectionObserver', () => {
    const mockIntersectionOptions = {
      rootMargin: 'BARBARCOLOUREDSHEEP',
      onIntersectionChange: expect.any(Function),
    };

    const options = {
      callback: jest.fn(),
      options: mockIntersectionOptions,
    };

    renderHook(() => useOnScreenWithDelayOnce<HTMLDivElement>(options));

    expect(useOnScreen).toHaveBeenCalledWith(mockIntersectionOptions);
  });
});
