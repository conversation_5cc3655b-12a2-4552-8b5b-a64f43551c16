import useRecentlyContactedCompanyIds from '@hooks/use-recently-contacted-company-ids';
import { renderHook } from '@testing-library/react';
import { getTradeContactHistory } from '@utils/trade-contact-history';
import { type Job } from 'lib/job/job.schema';
import { useGetJobs } from 'lib/job/job.hooks';
import { useIsAuthenticated } from 'lib/auth/auth.hooks';
import { POSTCODE_HEAD_OFFICE } from 'internals/mocks/common.constants';

type mockTrade = {
  id: string;
  createdAt: string;
  companyId: number;
};

jest.mock('lib/job/job.hooks', () => ({
  useGetJobs: jest.fn(),
}));

jest.mock('@utils/trade-contact-history', () => ({
  getTradeContactHistory: jest.fn(),
}));

jest.mock('lib/auth/auth.hooks', () => ({
  useIsAuthenticated: jest.fn(),
}));

const mockTrades = [
  {
    id: 'Trade1',
    createdAt: '2023-07-02',
    companyId: 38952,
  },
  {
    id: 'Trade2',
    createdAt: '2023-07-03',
    companyId: 54612,
  },
  {
    id: 'Trade3',
    createdAt: '2023-07-04',
    companyId: 29321,
  },
  {
    id: 'Trade4',
    createdAt: '2023-07-05',
    companyId: 82499,
  },
];
const mockCompanyIds = [1234, 1010, 9876];

const generateMockJobs = (trades: mockTrade[]): Job[] => {
  return trades.map(({ id, createdAt, companyId }) => ({
    id,
    description: '',
    category: {
      id: 1,
      label: 'Category',
    },
    status: 'Submitted',
    createdAt: new Date(createdAt).toISOString(),
    trades: [
      {
        id: companyId,
        status: 'Active',
        name: 'Trade Name',
        reviews: {
          score: 5,
          count: 10,
        },
        tradeApp: true,
        contact: {
          label: 'Contact Label',
          number: 1234567890,
        },
        opportunityId: 'Opportunity1',
        uniqueName: 'Unique Trade Name',
        logoUrl: 'http://example.com/logo.png',
        address: {
          postcode: POSTCODE_HEAD_OFFICE,
        },
        channelId: 'Channel1',
      },
    ],
  }));
};

const setupJobs = (mockJobs: Job[]) => {
  (useGetJobs as any).mockImplementation(() => ({
    jobs: mockJobs,
  }));
};

const setupTradeContactHistory = (contactedTradeIds: number[] = []) => {
  (getTradeContactHistory as any).mockImplementation(() => contactedTradeIds);
};

const setupNoJobs = () => {
  setupJobs([]);
};

const runAccountContactsTest = (trades: mockTrade[] = []) => {
  const expectedCompanyIds = trades.length
    ? trades.map(trade => trade.companyId).reverse()
    : [];
  if (trades.length) {
    setupJobs(generateMockJobs(trades));
  } else {
    setupNoJobs();
  }

  const {
    result: { current: companyIds },
  } = renderHook(() =>
    useRecentlyContactedCompanyIds({
      account: true,
      local: false,
      fallbackToLocal: false,
    }),
  );

  expect(useGetJobs).toHaveBeenCalled();
  expect(getTradeContactHistory).not.toHaveBeenCalled();
  expect(companyIds).toEqual(expectedCompanyIds);
};

const runAllContactsTest = (
  trades: mockTrade[] = [],
  mockIds: number[] = [],
) => {
  if (trades.length) {
    setupJobs(generateMockJobs(trades));
  } else {
    setupNoJobs();
  }

  setupTradeContactHistory(mockIds);

  const expectedCompanyIds = [
    ...trades.map(trade => trade.companyId).reverse(),
    ...mockIds,
  ];

  const {
    result: { current: companyIds },
  } = renderHook(() =>
    useRecentlyContactedCompanyIds({
      account: true,
      local: true,
      fallbackToLocal: false,
    }),
  );

  expect(getTradeContactHistory).toHaveBeenCalled();
  expect(useGetJobs).toHaveBeenCalled();
  expect(companyIds).toEqual(expectedCompanyIds);
};

const runLocalContactsTest = (mockIds: number[] = []) => {
  setupTradeContactHistory(mockIds);
  setupNoJobs();

  const {
    result: { current: companyIds },
  } = renderHook(() =>
    useRecentlyContactedCompanyIds({
      account: false,
      local: true,
      fallbackToLocal: false,
    }),
  );

  expect(useGetJobs).toHaveBeenCalled();
  expect(getTradeContactHistory).toHaveBeenCalled();
  expect(companyIds).toEqual(mockIds);
};

const runFallBackToContactsTest = (
  trades: mockTrade[] = [],
  mockIds: number[] = [],
  isAuthenticated: boolean,
) => {
  (useIsAuthenticated as jest.Mock).mockImplementation(() => isAuthenticated);

  setupJobs(generateMockJobs(trades));
  setupTradeContactHistory(mockIds);

  let expectedCompanyIds: number[] = [];

  if (isAuthenticated) {
    expectedCompanyIds = trades.map(trade => trade.companyId).reverse();
  } else {
    expectedCompanyIds = mockIds;
  }

  const {
    result: { current: companyIds },
  } = renderHook(() =>
    useRecentlyContactedCompanyIds({
      account: true,
      local: false,
      fallbackToLocal: true,
    }),
  );

  expect(getTradeContactHistory).toHaveBeenCalled();
  expect(useGetJobs).toHaveBeenCalled();
  expect(companyIds).toEqual(expectedCompanyIds);
};

describe('Using use-recently-contacted-company-ids', () => {
  describe('When user is logged in', () => {
    beforeEach(() => {
      (useIsAuthenticated as jest.Mock).mockImplementation(() => true);
    });

    it('returns an array of companyIds and all jobs in sorted order if trades have been contacted', () => {
      runAccountContactsTest(mockTrades);
    });

    it('returns empty arrays if no trades were contacted', () => {
      runAccountContactsTest();
    });
  });

  describe('When user is not logged in', () => {
    beforeEach(() => {
      (useIsAuthenticated as jest.Mock).mockImplementation(() => false);
    });

    it('returns an array of comanyIds if trades were contacted', () => {
      runLocalContactsTest(mockCompanyIds);
    });

    it('returns an empty array if no trades have been contacted', () => {
      runLocalContactsTest();
    });
  });

  describe('When the user is logged in and has companyIds in localStorage', () => {
    beforeEach(() => {
      (useIsAuthenticated as jest.Mock).mockImplementation(() => true);
    });

    it('returns an array of companyIds and all jobs in sorted order if the trades have been contacted', () => {
      runAllContactsTest(mockTrades, mockCompanyIds);
    });

    it('returns empty arrays if no trades were contacted', () => {
      runAllContactsTest();
    });
  });

  describe('When user is logged in or logged out and falls back to local contacts', () => {
    it('returns an array of company ids from localstorage', () => {
      runFallBackToContactsTest(mockTrades, mockCompanyIds, false);
    });

    it('returns an array of company ids from not from local storage', () => {
      runFallBackToContactsTest(mockTrades, mockCompanyIds, true);
    });
  });
});
