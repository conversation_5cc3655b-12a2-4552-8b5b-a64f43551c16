import { renderHook } from '@testing-library/react';
import React from 'react';
import { useOnScreen } from '../use-on-screen';

describe('@hooks/use-on-screen', () => {
  const mockObserve = jest.fn();
  const mockUnobserve = jest.fn();
  let caughtIntersectionObserverCallback: Function;
  const OriginalIO = IntersectionObserver;
  const onIntersectionChange = jest.fn();

  beforeAll(() => {
    // This should ideally be done with e2e tests that we don't yet have.
    // These tests are tightly coupled to functionality vs behaviour - but they
    // provide coverage.
    // @ts-ignore
    global.IntersectionObserver = jest.fn(cb => {
      caughtIntersectionObserverCallback = cb;
      return {
        observe: mockObserve,
        disconnect: mockUnobserve,
      };
    });
  });

  afterAll(() => {
    // @ts-ignore
    global.IntersectionObserver = OriginalIO;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should return if intersections are observed', () => {
    renderHook(() => useOnScreen<HTMLDivElement>({ onIntersectionChange }));

    caughtIntersectionObserverCallback([{ isIntersecting: true }]);

    expect(onIntersectionChange).toHaveBeenLastCalledWith(true);
  });

  it('should return if intersections are not observed', () => {
    renderHook(() => useOnScreen<HTMLDivElement>({ onIntersectionChange }));

    caughtIntersectionObserverCallback([{ isIntersecting: false }]);

    expect(onIntersectionChange).toHaveBeenLastCalledWith(false);
  });

  it('should return the created ref', () => {
    const mockRef = { current: 'foo' };

    jest.spyOn(React, 'useRef').mockReturnValueOnce(mockRef);

    const { result, unmount } = renderHook(() =>
      useOnScreen<HTMLDivElement>({ onIntersectionChange }),
    );

    const ref = result.current;

    expect(ref).toEqual(mockRef);
    expect(mockObserve).toHaveBeenLastCalledWith(mockRef.current);

    unmount();

    expect(mockUnobserve).toHaveBeenCalled();
  });

  it('should take the overrides and pass them on to the IntersectionObserver', () => {
    const options = {
      rootMargin: '5px',
      threshold: 0.1,
      root: null,
      onIntersectionChange,
    };

    const mockRef = { current: null };

    jest.spyOn(React, 'useRef').mockReturnValueOnce(mockRef);

    renderHook(() => useOnScreen<HTMLDivElement>(options));

    // @ts-ignore
    expect(global.IntersectionObserver).toHaveBeenLastCalledWith(
      caughtIntersectionObserverCallback,
      {
        root: null,
        rootMargin: '5px',
        threshold: 0.1,
      },
    );
  });

  it('should have some default set', () => {
    const mockRef = { current: null };

    jest.spyOn(React, 'useRef').mockReturnValueOnce(mockRef);

    renderHook(() => useOnScreen<HTMLDivElement>({ onIntersectionChange }));

    // @ts-ignore
    expect(global.IntersectionObserver).toHaveBeenLastCalledWith(
      caughtIntersectionObserverCallback,
      {
        root: null,
        rootMargin: '0px',
        threshold: 1,
      },
    );
  });
});
